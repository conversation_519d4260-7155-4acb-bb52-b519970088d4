#include "RegisterWidget.h"
#include "Components/TextBlock.h"
#include "Components/Button.h"
#include "Components/EditableTextBox.h"

void URegisterWidget::NativeConstruct()
{
	Super::NativeConstruct();
	
	if (RegisterButton)
	{
		RegisterButton->OnClicked.AddDynamic(this, &URegisterWidget::OnRegisterClicked);
	}
	
	if (BackButton)
	{
		BackButton->OnClicked.AddDynamic(this, &URegisterWidget::OnBackClicked);
	}
	
	// 清空错误信息
	ClearErrorMessage();
}

TObjectPtr<UButton> URegisterWidget::GetRegisterButton() const
{
	return RegisterButton;
}

TObjectPtr<UButton> URegisterWidget::GetBackButton() const
{
	return BackButton;
}

FString URegisterWidget::GetUsernameData() const
{
	if (UsernameEditableTextBox)
	{
		return UsernameEditableTextBox->GetText().ToString();
	}
	return FString();
}

FString URegisterWidget::GetPasswordData() const
{
	if (PasswordEditableTextBox)
	{
		return PasswordEditableTextBox->GetText().ToString();
	}
	return FString();
}

FString URegisterWidget::GetConfirmPasswordData() const
{
	if (ConfirmPasswordEditableTextBox)
	{
		return ConfirmPasswordEditableTextBox->GetText().ToString();
	}
	return FString();
}

FString URegisterWidget::GetEmailData() const
{
	if (EmailEditableTextBox)
	{
		return EmailEditableTextBox->GetText().ToString();
	}
	return FString();
}

void URegisterWidget::SetErrorMessage(const FString& Message)
{
	if (ErrorMessageText)
	{
		ErrorMessageText->SetText(FText::FromString(Message));
		ErrorMessageText->SetVisibility(ESlateVisibility::Visible);
	}
}

void URegisterWidget::ClearErrorMessage()
{
	if (ErrorMessageText)
	{
		ErrorMessageText->SetText(FText::GetEmpty());
		ErrorMessageText->SetVisibility(ESlateVisibility::Collapsed);
	}
}

void URegisterWidget::OnRegisterClicked()
{
	FString ErrorMessage;
	if (!ValidateInput(ErrorMessage))
	{
		SetErrorMessage(ErrorMessage);
		return;
	}
	
	ClearErrorMessage();
	// 注册逻辑将在MainController中处理
}

void URegisterWidget::OnBackClicked()
{
	// 返回逻辑将在MainController中处理
}

bool URegisterWidget::ValidateInput(FString& ErrorMessage)
{
	FString Username = GetUsernameData();
	FString Password = GetPasswordData();
	FString ConfirmPassword = GetConfirmPasswordData();
	FString Email = GetEmailData();
	
	// 验证用户名
	if (Username.IsEmpty() || Username.TrimStartAndEnd().IsEmpty())
	{
		ErrorMessage = TEXT("用户名不能为空");
		return false;
	}
	
	if (Username.Len() < 3 || Username.Len() > 20)
	{
		ErrorMessage = TEXT("用户名长度必须在3-20个字符之间");
		return false;
	}
	
	// 验证密码
	if (Password.IsEmpty() || Password.TrimStartAndEnd().IsEmpty())
	{
		ErrorMessage = TEXT("密码不能为空");
		return false;
	}
	
	if (Password.Len() < 6 || Password.Len() > 50)
	{
		ErrorMessage = TEXT("密码长度必须在6-50个字符之间");
		return false;
	}
	
	// 验证确认密码
	if (Password != ConfirmPassword)
	{
		ErrorMessage = TEXT("两次输入的密码不一致");
		return false;
	}
	
	// 验证邮箱格式（可选）
	if (!Email.IsEmpty())
	{
		if (!Email.Contains(TEXT("@")) || !Email.Contains(TEXT(".")))
		{
			ErrorMessage = TEXT("邮箱格式不正确");
			return false;
		}
	}
	
	return true;
}
