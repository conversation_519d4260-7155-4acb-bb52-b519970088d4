package Core

import (
	"fmt"
	"github.com/goccy/go-json"
	"github.com/panjf2000/gnet/v2"
	"log"
	"time"
)

type TSocket struct {
	gnet.BuiltinEventEngine
	Engine gnet.Engine
}

func (Self *TSocket) OnBoot(Engine gnet.Engine) gnet.Action {
	Self.Engine = Engine
	log.Printf("TCP服务器初始化")
	return gnet.None
}

func (Self *TSocket) OnTraffic(Connect gnet.Conn) gnet.Action {
	RawData, _ := Connect.Next(-1)
	Stream := NewNStream()
	log.Printf("TCP消息 %s\n", RawData)
	if Error := json.Unmarshal(RawData, &Stream); Error != nil {
		log.Println("Error Message : " + string(RawData))
		log.Printf("Error : %v \n", Error)
		return gnet.None
	}
	if Value, OK := Pool.Load(Stream.UserID); OK {
		Client := Value.(*NClient)
		Client.TCP.Connection = Connect
		Client.TCP.LastActive = time.Now()
	} else {
		Client := &NClient{
			TCP: TPack{
				Connection: Connect,
				LastActive: time.Now(),
			},
		}
		Pool.Store(Stream.UserID, Client)
	}
	Self.HandleStream(Connect, Stream)
	return gnet.None
}

func (Self *TSocket) Cleanup() {
	Ticker := time.NewTicker(CleanupInterval)
	defer Ticker.Stop()
	for range Ticker.C {
		Now := time.Now()
		Pool.Range(func(Key, Value interface{}) bool {
			Client := Value.(*NClient)
			if Now.Sub(Client.TCP.LastActive) > InactiveTimeout {
				Self.CleanUser(Key.(int))
				log.Printf("用户 %d TCP连接过期\n", Key.(int))
			}
			return true
		})
	}
}

func (Self *TSocket) CleanUser(UserID int) {
	Pool.Delete(UserID)
	Stream := NewNStream()
	Stream.UserID = UserID
	Stream.Type = "OnGuestLogout"
	Self.BroadcastStream(Stream)
}

func (Self *TSocket) Send(Message []byte, Connection gnet.Conn) {
	if Connection == nil {
		return
	}
	// log.Printf("TCP发出 %s\n", Message)
	Message = append(Message, '\n')
	Error := Connection.AsyncWrite(Message, func(Connect gnet.Conn, Error error) error {
		if Error != nil {
			log.Printf("发送失败: %v\n", Error)
		}
		return nil
	})
	if Error != nil {
		log.Printf("发送失败: %v\n", Error)
		return
	}
}

func (Self *TSocket) SendStream(Stream NStream, Connection gnet.Conn) {
	Message, _ := json.Marshal(Stream)
	Self.Send(Message, Connection)
}

func (Self *TSocket) BroadcastOther(Message []byte, SelfKey interface{}) {
	Pool.Range(func(Key, Value interface{}) bool {
		if SelfKey == Key {
			return true
		}
		Connection := Value.(*NClient).TCP.Connection
		Self.Send(Message, Connection)
		return true
	})
}

func (Self *TSocket) BroadcastStreamOther(Stream NStream) {
	Message, _ := json.Marshal(Stream)
	Self.BroadcastOther(Message, Stream.UserID)
}

func (Self *TSocket) Broadcast(Message []byte) {
	Self.BroadcastOther(Message, nil)
}

func (Self *TSocket) BroadcastStream(Stream NStream) {
	Message, _ := json.Marshal(Stream)
	Self.Broadcast(Message)
}

func StartTCP(Port int) {
	Instance := new(TSocket)
	go func() {
		go Instance.Cleanup()

		Url := fmt.Sprintf("tcp://0.0.0.0:%d", Port)
		Error := gnet.Run(
			Instance, Url,
			gnet.WithMulticore(true),
		)
		if Error != nil {
			log.Printf("存在错误: %v\n", Error)
			return
		}
	}()
	go func() {
		Ticker := time.NewTicker(CleanupInterval)
		for {
			<-Ticker.C
			Stream := NewNStream()
			Stream.UserID = -1
			Stream.Type = "Ping"
			Instance.BroadcastStream(Stream)
		}
	}()
}

func (Self *TSocket) HandleStream(Connect gnet.Conn, Stream NStream) {
	if Stream.Type == "Pong" {
		return
	}
	if Stream.Type == "OnUserRegister" {
		Self.HandleUserRegister(Connect, Stream)
		return
	}
	if Stream.Type == "OnUserLogin" {
		Self.HandleUserLogin(Connect, Stream)
		return
	}
	if Stream.Type == "OnGuestLogin" {
		if SpawnAt, OK := Stream.Payload["SpawnAt"].(string); OK {
			if Client, OK := Pool.Load(Stream.UserID); OK {
				Client.(*NClient).UDP.Motion = SpawnAt
				Pool.Range(func(Key, Value interface{}) bool {
					if Stream.UserID == Key {
						return true
					}
					Client := Value.(*NClient)
					Login := NewNStream()
					Login.UserID = Key.(int)
					Login.Type = "OnGuestLogin"
					Login.Payload["SpawnAt"] = Client.UDP.Motion
					Self.SendStream(Login, Connect)
					return true
				})
				log.Printf("用户 %d 登陆\n", Stream.UserID)
			}
		}
	}
	if Stream.Type == "OnGuestLogout" {
		Self.CleanUser(Stream.UserID)
		log.Printf("用户 %d TCP连接退出\n", Stream.UserID)
		return
	}
	if Stream.Type == "CheckDailyLetterLimit" {
		if _, exists := Pool.Load(Stream.UserID); exists {
			LimitReached, err := CheckDailyLetterLimit(int(Stream.UserID), 3)
			if err != nil {
				log.Printf("信件限制检查失败: %v", err)
				return
			}
			response := NewNStream()
			response.Type = "OnCheckDailyLetterLimit"
			response.Payload["LimitReached"] = LimitReached
			Self.SendStream(response, Connect)
		}
		return
	}
	if Stream.Type == "LetterData" {
		log.Printf("收到信件\n")
		if _, exists := Pool.Load(Stream.UserID); exists {
			if Content, OK := Stream.Payload["Content"].(string); OK {
				if StyleIndexFloat, OK := Stream.Payload["StyleIndex"].(float64); OK {
					if Location, OK := Stream.Payload["Location"].(string); OK {
						if Rotation, OK := Stream.Payload["Rotation"].(string); OK {
							letter, err := SaveLetter(int(Stream.UserID) ,Content, int(StyleIndexFloat), Location, Rotation)
							if err != nil {
								log.Printf("保存信件失败: %v\n", err)
								return
							}
							log.Printf("%s\n", Content)
							response := NewNStream()
							response.Type = "OnLetterDataReceived"
							response.Payload = map[string]interface{}{
								"Letters": []Letter{letter},
							}
							Self.BroadcastStream(response)
							return
						}
					}
				}
			}
		}
	}
	if Stream.Type == "RequestLetterObjects" {
		log.Printf("收到请求信件列表\n")
		letters, err := GetLatestLetters(10)
		if err != nil {
			log.Printf("获取信件列表失败: %v\n", err)
			return
		}

		// 将所有信件打包成一条消息发送
		response := NewNStream()
		response.Type = "OnLetterDataReceived"
		response.Payload = map[string]interface{}{
			"Letters": letters,
		}
		Self.SendStream(response, Connect)
		log.Printf("已发送 %d 封信件\n", len(letters))
		return
	}
	Self.BroadcastStreamOther(Stream)
}

// 处理用户注册请求
func (Self *TSocket) HandleUserRegister(Connect gnet.Conn, Stream NStream) {
	log.Printf("收到用户注册请求\n")

	response := NewNStream()
	response.Type = "OnRegisterResponse"

	// 提取注册信息
	username, usernameOK := Stream.Payload["Username"].(string)
	password, passwordOK := Stream.Payload["Password"].(string)
	email, emailOK := Stream.Payload["Email"].(string)

	if !usernameOK || !passwordOK {
		response.Payload["Success"] = false
		response.Payload["Message"] = "注册信息不完整"
		Self.SendStream(response, Connect)
		return
	}

	// 如果没有提供邮箱，设置为空字符串
	if !emailOK {
		email = ""
	}

	// 调用注册函数
	user, err := RegisterUser(username, password, email)
	if err != nil {
		response.Payload["Success"] = false
		response.Payload["Message"] = err.Error()
		Self.SendStream(response, Connect)
		log.Printf("用户注册失败: %v\n", err)
		return
	}

	// 注册成功
	response.Payload["Success"] = true
	response.Payload["Message"] = "注册成功"
	response.Payload["UserID"] = user.UserID
	response.Payload["Username"] = user.Username
	Self.SendStream(response, Connect)
	log.Printf("用户 %s 注册成功，ID: %d\n", user.Username, user.UserID)
}

// 处理用户登录请求
func (Self *TSocket) HandleUserLogin(Connect gnet.Conn, Stream NStream) {
	log.Printf("收到用户登录请求\n")

	response := NewNStream()
	response.Type = "OnLoginResponse"

	// 提取登录信息
	username, usernameOK := Stream.Payload["Username"].(string)
	password, passwordOK := Stream.Payload["Password"].(string)

	if !usernameOK || !passwordOK {
		response.Payload["Success"] = false
		response.Payload["Message"] = "登录信息不完整"
		Self.SendStream(response, Connect)
		return
	}

	// 调用认证函数
	user, err := AuthenticateUser(username, password)
	if err != nil {
		response.Payload["Success"] = false
		response.Payload["Message"] = err.Error()
		Self.SendStream(response, Connect)
		log.Printf("用户登录失败: %v\n", err)
		return
	}

	// 登录成功
	response.UserID = int(user.UserID)
	response.Payload["Success"] = true
	response.Payload["Message"] = "登录成功"
	response.Payload["UserID"] = user.UserID
	response.Payload["Username"] = user.Username
	Self.SendStream(response, Connect)
	log.Printf("用户 %s 登录成功，ID: %d\n", user.Username, user.UserID)
}
