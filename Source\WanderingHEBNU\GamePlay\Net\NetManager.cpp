#include "NetManager.h"

#include "../../Character/Guest/GuestCharacter.h"
#include "../../Character/Main/MainController.h"
#include "../../Character/Main/MainCharacter.h"
#include "../../GamePlay/MainGameInstance.h"
#include "../../Common/Log.h"
#include "../../Prop/LetterObject.h"

ANetManager::ANetManager()
{
	PrimaryActorTick . bCanEverTick = true;

	UDPSocketComponent = CreateDefaultSubobject<UUDPSocketComponent>(TEXT("UDPSocketComponent"));
	TCPSocketComponent = CreateDefaultSubobject<UTCPSocketComponent>(TEXT("TCPSocketComponent"));
}

void ANetManager::BeginPlay()
{
	Super::BeginPlay();
	UDPSocketComponent -> GetOnStartDelegate() . AddDynamic(this, &ThisClass::OnUDPSocketStart);
	UDPSocketComponent -> GetOnCloseDelegate() . AddDynamic(this, &ThisClass::OnUDPSocketClose);
	UDPSocketComponent -> GetOnReceivedMessageDelegate() . AddDynamic(this, &ThisClass::OnUDPReceivedMessage);
	UDPSocketComponent -> GetOnReceivedMessageInGameThreadDelegate() . AddDynamic(this, &ThisClass::OnUDPReceivedMessageInGameThread);
	TCPSocketComponent -> GetOnStartDelegate() . AddDynamic(this, &ThisClass::OnTCPSocketStart);
	TCPSocketComponent -> GetOnCloseDelegate() . AddDynamic(this, &ThisClass::OnTCPSocketClose);
	TCPSocketComponent -> GetOnReceivedMessageDelegate() . AddDynamic(this, &ThisClass::OnTCPReceivedMessage);
	TCPSocketComponent -> GetOnReceivedMessageInGameThreadDelegate() . AddDynamic(this, &ThisClass::OnTCPReceivedMessageInGameThread);
	MainGameInstance = Cast<UMainGameInstance>(GetGameInstance());
	LocalController = Cast<AMainController>(GetWorld() -> GetFirstPlayerController());
	LocalCharacter = Cast<AMainCharacter>(LocalController -> GetCharacter());

	if (LocalController)
	{
		LettersSubclassOf = LocalController -> LettersSubclassOf;
	}
}

void ANetManager::BeginDestroy()
{
	SendLogoutAll();
	UDPSocketComponent -> Close();
	TCPSocketComponent -> Close();
	Super::BeginDestroy();
}

void ANetManager::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
	Super::EndPlay(EndPlayReason);
}

void ANetManager::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);
}

void ANetManager::Start()
{
	//UserID = Value;
	UDPSocketComponent -> Start();
	TCPSocketComponent -> Start();
}

void ANetManager::AddGuest(int ID, AGuestCharacter* Guest)
{
	if (GuestMap . Contains(ID))
	{
		if (GuestMap[ID] && IsValid(GuestMap[ID])) { GuestMap[ID] -> Destroy(); }
		GuestMap[ID] = Guest;
		return;
	}
	GuestMap . Emplace(ID, Guest);
}

AGuestCharacter* ANetManager::GetGuest(const int ID)
{
	if (!GuestMap . Contains(ID)) { return nullptr; }
	return GuestMap[ID];
}

void ANetManager::SendStream(const NetPacket::FStream& Stream, const ENetType Type) const
{
	switch (Type)
	{
		case ENetType::UDP:
			{
				if (!UDPSocketComponent -> SendStream(Stream))
				{
					ULog::Info(TEXT("【Net】发送UDP业务逻辑失败"), true);
				}
				break;
			}
		case ENetType::TCP:
			{
				if (!TCPSocketComponent -> SendStream(Stream))
				{
					ULog::Info(TEXT("【Net】发送TCP业务逻辑失败"), true);
				}
				break;
			}
		default: break;
	}
}

void ANetManager::SendPong(const ENetType Type) const
{
	NetPacket::FStream Stream;
	Stream . SetUserID(UserID);
	Stream . SetType("Pong");
	Stream . Serialize();
	SendStream(Stream, Type);
}

void ANetManager::SendLogin()
{
	if (!LocalCharacter) { return; }
	const FTransform Transform = LocalCharacter -> GetActorTransform();
	NetPacket::FGuestLogin GuestLogin;
	GuestLogin . SetSpawnAt(Transform);
	ULog::Info(LocalController->GetPassword(), false);
	GuestLogin . SetPassword(LocalController -> GetPassword());
	NetPacket::FStream Stream;
	Stream . SetUserID(LocalController->GetUsername());
	Stream . SetType("OnGuestLogin");
	Stream . SetPayload(GuestLogin . Serialize());
	Stream . Serialize();
	SendStream(Stream, ENetType::TCP);
	ULog::Info(TEXT("【OnSendLoginRequest】发送登录请求完成"), false);
}

void ANetManager::SendLogout(const ENetType Type)
{
	NetPacket::FStream Stream;
	Stream . SetUserID(UserID);
	Stream . SetType("OnGuestLogout");
	Stream . Serialize();
	SendStream(Stream, Type);
}

void ANetManager::SendLogoutAll()
{
	SendLogout(ENetType::UDP);
	SendLogout(ENetType::TCP);
}

void ANetManager::SendMotion()
{
	if (!LocalCharacter) { return; }
	const FTransform Transform = LocalCharacter -> GetActorTransform();
	const UMainAnimInstance* MainAnimInstance = LocalCharacter -> GetAnimInstance();
	NetPacket::FGuestMotion GuestMotion;
	if (MainAnimInstance)
	{
		GuestMotion . SetIsInAir(MainAnimInstance -> IsInAir());
		GuestMotion . SetIsWaving(MainAnimInstance -> IsWaving());
	}
	GuestMotion . SetMotion(Transform);
	if (LastMotionRep . Equals(GuestMotion)) { return; }
	NetPacket::FStream Stream;
	Stream . SetUserID(UserID);
	Stream . SetType("OnGuestMotion");
	Stream . SetPayload(GuestMotion . Serialize());
	Stream . Serialize();
	SendStream(Stream, ENetType::UDP);
	LastMotionRep = GuestMotion;
}

void ANetManager::CheckDailyLetterLimit()
{
	NetPacket::FStream Stream;
	Stream.SetUserID(UserID);
	Stream.SetType("CheckDailyLetterLimit");
	Stream.Serialize();
	SendStream(Stream, ENetType::TCP);
}

void ANetManager::SendLetterData(NetPacket::FLetterData& Letter) const
{
	NetPacket::FStream Stream;
	Stream . SetUserID(UserID);
	Stream . SetType("LetterData");
	Stream . SetPayload(Letter . Serialize());
	Stream . Serialize();
	ULog::Info(Stream.ToString(), false);
	SendStream(Stream, ENetType::TCP);
	ULog::Info(TEXT("【OnSendLetterData】发送信件数据完成"), false);
}

void ANetManager::OnUDPSocketStart(const FString& BoundIP, int BoundPort)
{
	SendPong(ENetType::UDP);
}

void ANetManager::OnUDPSocketClose()
{
	SendLogout(ENetType::UDP);
}

void ANetManager::OnUDPReceivedMessage(const FString& Message, const FString& Endpoint, const int Port)
{
	const NetPacket::FStream Stream = NetPacket::FStream(Message);
	const int GuestID = Stream . GetUserID();
	const FString Type = Stream . GetType();
	const TSharedPtr<FJsonObject> Payload = Stream . GetPayload();
	if (GuestID == UserID) { return; }
	if (Type . Equals("Ping")) { return SendPong(ENetType::UDP); }
}

void ANetManager::OnUDPReceivedMessageInGameThread(const FString& Message, const FString& Endpoint, const int Port)
{
	const NetPacket::FStream Stream = NetPacket::FStream(Message);
	const int GuestID = Stream . GetUserID();
	const FString Type = Stream . GetType();
	const TSharedPtr<FJsonObject> Payload = Stream . GetPayload();
	if (GuestID == UserID) { return; }
	if (Type . Equals("OnGuestMotion"))
	{
		return OnGuestMotion(GuestID, NetPacket::FGuestMotion(Payload));
	}
}

void ANetManager::OnTCPSocketStart(const FString& BoundIP, int BoundPort)
{
	//SendLogin();
}

void ANetManager::OnTCPSocketClose()
{
	SendLogout(ENetType::TCP);
}

void ANetManager::OnTCPReceivedMessage(const FString& Message, const FString& Endpoint, const int Port)
{
	const NetPacket::FStream Stream = NetPacket::FStream(Message);
	const int GuestID = Stream . GetUserID();
	const FString Type = Stream . GetType();
	const TSharedPtr<FJsonObject> Payload = Stream . GetPayload();
	if (GuestID == UserID) { return; }
	if (Type . Equals("Ping")) { return SendPong(ENetType::TCP); }
}

void ANetManager::OnTCPReceivedMessageInGameThread(const FString& Message, const FString& Endpoint, const int Port)
{
	const NetPacket::FStream Stream = NetPacket::FStream(Message);
	const int GuestID = Stream . GetUserID();
	const FString Type = Stream . GetType();
	const TSharedPtr<FJsonObject> Payload = Stream . GetPayload();
	if (GuestID == UserID) { return; }
	if (Type.Equals("OnLoginResponse"))
	{
		ULog::Info(TEXT("【OnLoginResponseReceived】收到登录反应信息"), false);
		bool bSuccess = NetPacket::JsonGetBool(Payload, "Success");
		FString Msg = NetPacket::JsonGetString(Payload, "Message");
		int UserName = NetPacket::JsonGetNumber(Payload, "UserID");
		if (bSuccess)
		{
			this->UserID = UserName;
		}
		OnLoginResult.Broadcast(bSuccess, Msg);
		LoginResult = bSuccess;
	}
	if (Type.Equals("OnRegisterResponse"))
	{
		ULog::Info(TEXT("【OnRegisterResponseReceived】收到注册反应信息"), false);
		bool bSuccess = NetPacket::JsonGetBool(Payload, "Success");
		FString Msg = NetPacket::JsonGetString(Payload, "Message");
		OnRegisterResult.Broadcast(bSuccess, Msg);
	}
	if (Type . Equals("OnGuestLogin"))
	{
		return OnGuestLogin(GuestID, NetPacket::FGuestLogin(Payload));
	}
	if (Type . Equals("OnGuestLogout"))
	{
		return OnGuestLogout(GuestID);
	}
	if (Type.Equals("OnCheckDailyLetterLimit"))
	{
		const bool bLimitReached = Payload->GetBoolField(TEXT("LimitReached"));
		if (LocalController)
		{
			LocalController->HandleOnCheckDailyLetterLimit(bLimitReached);
		}
	}
	if (Type . Equals("OnLetterDataReceived"))
	{
		ULog::Info(TEXT("【OnLetterDataReceived】收到信件数据List"), false);
		ULog::Info(Message, false);

		if (LettersSubclassOf . IsEmpty()) { return; }
		const TArray<TSharedPtr<FJsonValue>>* JsonArray;
		if (Payload -> TryGetArrayField(TEXT("Letters"), JsonArray))
		{
			for (const TSharedPtr<FJsonValue>& JsonValue : *JsonArray)
			{
				NetPacket::FLetterData LetterData(JsonValue -> AsObject());
				OnLetterDataReceived(LetterData);
			}
		}
	}
}

void ANetManager::OnLetterDataReceived(const NetPacket::FLetterData& LetterData) const
{
	ULog::Info(TEXT("【OnLetterDataReceived】收到信件数据"), true);
	ULog::Info(LetterData . GetContent(), true);
	const int Index = LetterData . GetStyleIndex();
	if (!LettersSubclassOf . IsValidIndex(Index) || !LettersSubclassOf[Index]) { return; }
	const FVector Location = LetterData . GetLocation();
	const FRotator Rotation = LetterData . GetRotation();
	const FString Content = LetterData . GetContent();

	ALetterObject* Letter = GetWorld() -> SpawnActor<ALetterObject>(LettersSubclassOf[Index], Location, Rotation);
	if (Letter)
	{
		Letter -> SetLetterContent(Content);
		Letter -> SetLetterStyleIndex(Index);
		Letter -> SetLetterLocation(Location);
		Letter -> SetLetterRotation(Rotation);
	}
}

void ANetManager::OnGuestLogin(const int GuestID, const NetPacket::FGuestLogin& Value)
{
	if (!GuestSubclassOf) { return; }
	FActorSpawnParameters Parameter;
	Parameter . Owner = this;
	Parameter . bNoFail = true;
	Parameter . SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AlwaysSpawn;
	const FVector Location = Value . GetSpawnAt() . GetLocation();
	const FRotator Rotation = Value . GetSpawnAt() . GetRotation() . Rotator();
	NetPacket::FGuestMotion Motion;
	Motion . SetMotion(Value . GetSpawnAt());
	AGuestCharacter* GuestCharacter = GetWorld() -> SpawnActor<AGuestCharacter>(GuestSubclassOf, Location, Rotation, Parameter);
	GuestCharacter -> SetUserID(GuestID);
	GuestCharacter -> SetTargetMotion(Motion);
	AddGuest(GuestID, GuestCharacter);
	ULog::Info(TEXT("【OnGuestLogin】玩家登陆 UID : ") + FString::FromInt(GuestID), true);
}

void ANetManager::RequestLetterObjects() const
{
	NetPacket::FStream Stream;
	Stream . SetUserID(UserID);
	Stream . SetType("RequestLetterObjects");
	Stream . Serialize();
	SendStream(Stream, ENetType::TCP);
}

void ANetManager::SendLoginRequest(NetPacket::FLoginPayload& Login) const
{

}

void ANetManager::SendRegister(const FString& Username, const FString& Password, const FString& Email)
{
	NetPacket::FRegisterPayload RegisterPayload;
	RegisterPayload.SetUsername(Username);
	RegisterPayload.SetPassword(Password);
	RegisterPayload.SetEmail(Email);

	NetPacket::FStream Stream;
	Stream.SetUserID(-1); // 注册时还没有用户ID
	Stream.SetType("OnUserRegister");
	Stream.SetPayload(RegisterPayload.Serialize());
	Stream.Serialize();

	SendStream(Stream, ENetType::TCP);
	ULog::Info(TEXT("【OnSendRegisterRequest】发送注册请求完成"), false);
}

void ANetManager::SendUserLogin(const FString& Username, const FString& Password)
{
	NetPacket::FLoginPayload LoginPayload;
	LoginPayload.SetUsername(Username);
	LoginPayload.SetPassword(Password);

	NetPacket::FStream Stream;
	Stream.SetUserID(-1); // 登录时还没有用户ID
	Stream.SetType("OnUserLogin");
	Stream.SetPayload(LoginPayload.Serialize());
	Stream.Serialize();

	SendStream(Stream, ENetType::TCP);
	ULog::Info(TEXT("【OnSendUserLoginRequest】发送用户登录请求完成"), false);
}

void ANetManager::OnGuestLogout(const int GuestID)
{
	if (!GuestMap . Contains(GuestID)) { return; }
	if (GuestMap[GuestID] && IsValid(GuestMap[GuestID])) { GuestMap[GuestID] -> Destroy(); }
	GuestMap . Remove(GuestID);
	ULog::Info(TEXT("【OnGuestLogout】玩家退出 UID : ") + FString::FromInt(GuestID), true);
}

void ANetManager::OnGuestMotion(const int GuestID, const NetPacket::FGuestMotion& Value)
{
	AGuestCharacter* GuestCharacter = GetGuest(GuestID);
	if (!GuestCharacter || !IsValid(GuestCharacter)) { return; }
	GuestCharacter -> SetTargetMotion(Value);
}

