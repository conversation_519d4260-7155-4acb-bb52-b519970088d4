#pragma once

#include "CoreMinimal.h"
#include "Components/EditableTextBox.h"
#include "Components/Button.h"
#include "WanderingHEBNU/Base/BaseUserWidget.h"
#include "RegisterWidget.generated.h"

UCLASS()
class URegisterWidget : public UBaseUserWidget
{
	GENERATED_BODY()
	
public:
	virtual void NativeConstruct() override;

	TObjectPtr<UButton> GetRegisterButton() const;
	
	TObjectPtr<UButton> GetBackButton() const;

	FString GetUsernameData() const;

	FString GetPasswordData() const;

	FString GetConfirmPasswordData() const;

	FString GetEmailData() const;

	void SetErrorMessage(const FString& Message);

	void ClearErrorMessage();
	
protected:
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "注册按钮", meta = (BindWidget))
	TObjectPtr<UButton> RegisterButton;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "返回按钮", meta = (BindWidget))
	TObjectPtr<UButton> BackButton;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "用户名文本框", meta = (BindWidget))
	TObjectPtr<UEditableTextBox> UsernameEditableTextBox;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "密码文本框", meta = (BindWidget))
	TObjectPtr<UEditableTextBox> PasswordEditableTextBox;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "确认密码文本框", meta = (BindWidget))
	TObjectPtr<UEditableTextBox> ConfirmPasswordEditableTextBox;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "邮箱文本框", meta = (BindWidget))
	TObjectPtr<UEditableTextBox> EmailEditableTextBox;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "错误信息文本", meta = (BindWidget))
	TObjectPtr<class UTextBlock> ErrorMessageText;

	UFUNCTION(BlueprintCallable, Category = "注册操作")
	void OnRegisterClicked();

	UFUNCTION(BlueprintCallable, Category = "注册操作")
	void OnBackClicked();

	// 输入验证函数
	bool ValidateInput(FString& ErrorMessage);
	
};
