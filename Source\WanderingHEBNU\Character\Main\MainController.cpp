#include "MainController.h"

#include "../../GamePlay/MainGameInstance.h"
#include "../../GamePlay/Net/NetManager.h"
#include "../../Prop/LetterObject.h"
#include "MainCharacter.h"
#include "Blueprint/UserWidget.h"
#include "Components/Button.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "../../UI/Widget/ReadLetterWidget.h"
#include "../../UI/Widget/SendLetterWidget.h"
#include "../../UI/Widget/MainMenuWidget.h"
#include "../../UI/Widget/ExitWidget.h"
#include "Components/CapsuleComponent.h"
#include "Kismet/KismetSystemLibrary.h"
#include "WanderingHEBNU/UI/Widget/HeadInfoWidget.h"
#include "WanderingHEBNU/UI/Widget/LoginWidget.h"
#include "WanderingHEBNU/UI/Widget/RegisterWidget.h"

AMainController::AMainController()
{
	bIsSprinting = false;
}

void AMainController::BeginPlay()
{
	Super::BeginPlay();
	MainCharacter = Cast<AMainCharacter>(GetCharacter());
	if (MainCharacter)
	{
		MainGameInstance = Cast<UMainGameInstance>(GetGameInstance());
	}
	if (UEnhancedInputLocalPlayerSubsystem* InputSystem = GetLocalPlayer() -> GetSubsystem<UEnhancedInputLocalPlayerSubsystem>())
	{
		InputSystem -> AddMappingContext(InputMappingContext, 0);
	}

	if (MenuSubclassOf)
	{
		MainMenuWidget = CreateWidget<UMainMenuWidget>(GetWorld(),MenuSubclassOf);
		if (MainMenuWidget)
		{
			MainMenuWidget->AddToViewport(100);
			MainMenuWidget->Open();

			if (UButton* LoginBtn = MainMenuWidget->GetLoginButton())
			{
				LoginBtn->OnClicked.AddDynamic(this, &AMainController::OnLoginRequest);
			}
			if (UButton* RegisterBtn = MainMenuWidget->GetRegisterButton())
			{
				RegisterBtn->OnClicked.AddDynamic(this, &AMainController::OnRegisterRequest);
			}
		}
	}

	if (MainGameInstance)
	{
		if (ANetManager* Manager = MainGameInstance->GetNetManager())
		{
			if (!Manager->OnLoginResult.IsAlreadyBound(this, &AMainController::OnLoginVerify))
			{
				Manager->OnLoginResult.AddDynamic(this, &AMainController::OnLoginVerify);
			}
			if (!Manager->OnRegisterResult.IsAlreadyBound(this, &AMainController::OnRegisterVerify))
			{
				Manager->OnRegisterResult.AddDynamic(this, &AMainController::OnRegisterVerify);
			}
			if (UButton* ExitBtn=MainMenuWidget->GetExitButton())
			{
				ExitBtn->OnClicked.AddDynamic(this, &AMainController::ExitGame);
			}
		}
	}

	HeadInfoWidgetInstance = CreateWidget<UHeadInfoWidget>(GetWorld(), HeadInfoWidget);
	if (HeadInfoWidgetInstance)
	{
		FString Info = TEXT("按R键读取信件");
		HeadInfoWidgetInstance -> SetInfo(Info);
	}
}

void AMainController::BeginDestroy()
{
	if (MainGameInstance)
	{
		if (ANetManager* Manager = MainGameInstance -> GetNetManager())
		{
			Manager -> SendLogoutAll();
		}
	}
	Super::BeginDestroy();
}

void AMainController::SetupInputComponent()
{
	Super::SetupInputComponent();
	if (UEnhancedInputComponent* EnhancedInputComponent = Cast<UEnhancedInputComponent>(InputComponent))
	{
		EnhancedInputComponent -> BindAction(MoveAction, ETriggerEvent::Triggered, this, &AMainController::Move);
		EnhancedInputComponent -> BindAction(LookAction, ETriggerEvent::Triggered, this, &AMainController::Look);
		EnhancedInputComponent -> BindAction(JumpAction, ETriggerEvent::Triggered, this, &AMainController::Jump);
		EnhancedInputComponent -> BindAction(SendLetterAction, ETriggerEvent::Triggered, this, &AMainController::CheckLetterLimit);
		EnhancedInputComponent -> BindAction(ReadLetterAction, ETriggerEvent::Triggered, this, &AMainController::ReadLetter);
		EnhancedInputComponent -> BindAction(WaveHandAction, ETriggerEvent::Triggered, this, &AMainController::WaveHand);
		EnhancedInputComponent -> BindAction(SprintAction, ETriggerEvent::Triggered, this, &AMainController::ToggleSprint);
		EnhancedInputComponent -> BindAction(ExitAction, ETriggerEvent::Triggered, this, &AMainController::ExitGame);
	}
}

void AMainController::Tick(float DeltaSeconds)
{
	Super::Tick(DeltaSeconds);

	const FVector Start = MainCharacter -> GetActorLocation() - FVector(0, 0, MainCharacter -> GetCapsuleComponent() -> GetScaledCapsuleHalfHeight());
	const FVector ForwardVector = MainCharacter -> GetActorForwardVector();
	const FVector End = ((ForwardVector * 200.f) + Start);

	FHitResult HitResult;
	FCollisionQueryParams CollisionParams;
	CollisionParams . AddIgnoredActor(GetOwner());

	const bool bHitLetter = GetWorld()->LineTraceSingleByChannel(HitResult, Start, End, ECC_Visibility, CollisionParams);

	const ALetterObject* HitLetterObject = bHitLetter ? Cast<ALetterObject>(HitResult.GetActor()) : nullptr;

	const bool bShouldShowTip = (HitLetterObject != nullptr) && !bIsRead && !bIsSend;
	const bool bShouldHideTip = (!bHitLetter) || (bIsRead || bIsSend);

	if (HeadInfoWidgetInstance)
	{
		if (bShouldShowTip && !bIsOpenTip)
		{
			HeadInfoWidgetInstance->AddToViewport();
			bIsOpenTip = true;
		}
		else if (bShouldHideTip && bIsOpenTip)
		{
			HeadInfoWidgetInstance->RemoveFromParent();
			bIsOpenTip = false;
		}
	}
	else if (bIsOpenTip) // 安全保护：当Widget被销毁时自动关闭状态
	{
		bIsOpenTip = false;
	}
	// UE_LOG(LogTemp, Warning, TEXT("READ: %s"), bIsRead ? TEXT("true") : TEXT("false"));
	// UE_LOG(LogTemp, Warning, TEXT("SEND: %s"), bIsSend ? TEXT("true") : TEXT("false"));
	// UE_LOG(LogTemp, Warning, TEXT("tip: %s"), bIsOpenTip ? TEXT("true") : TEXT("false"));
}

void AMainController::Move(const FInputActionValue& Value)
{
	const FVector2D Input = Value . Get<FVector2D>();
	const FRotator Rotation = GetControlRotation();
	const FRotator YawRotation(0, Rotation . Yaw, 0);
	const FVector Forward = FRotationMatrix(YawRotation) . GetUnitAxis(EAxis::X);
	const FVector Right = FRotationMatrix(YawRotation) . GetUnitAxis(EAxis::Y);
	GetCharacter() -> AddMovementInput(Forward, Input . Y);
	GetCharacter() -> AddMovementInput(Right, Input . X);
}

void AMainController::Look(const FInputActionValue& Value)
{
	const FVector2D Input = Value . Get<FVector2D>();
	AddYawInput(Input . X);
	AddPitchInput(Input . Y);
}

void AMainController::Jump()
{
	MainCharacter -> Jump();
}

void AMainController::SendLetter()
{
	bIsSend = true;
	MainCharacter -> GetCharacterMovement() -> StopMovementImmediately();
	MainCharacter -> GetCharacterMovement() -> SetMovementMode(MOVE_None);

	// 初始化UI
	SendLetterWidgetInstance = CreateWidget<USendLetterWidget>(GetWorld(), SendLetterWidget);
	if (SendLetterWidgetInstance)
	{
		SendLetterWidgetInstance -> AddToViewport();
		SendLetterWidgetInstance -> Open();

		SendLetterWidgetInstance -> SetInputUI();

		if (UButton* GenerateButton = SendLetterWidgetInstance -> GetGenerateButton())
		{
			GenerateButton -> OnClicked . AddDynamic(this, &AMainController::OnGenerateLetter);
		}
		if (UButton* CancelButton = SendLetterWidgetInstance -> GetCancelButton())
		{
			CancelButton -> OnClicked . AddDynamic(this, &AMainController::OnCancel);
		}
	}
}

void AMainController::CheckLetterLimit()
{
	if (MainGameInstance)
	{
		if (ANetManager* Manager = MainGameInstance->GetNetManager())
		{
			Manager->CheckDailyLetterLimit();
		}
	}
}

void AMainController::HandleOnCheckDailyLetterLimit(bool bLimitReached)
{
	if (bLimitReached)
	{
		GEngine->AddOnScreenDebugMessage(-1, 5, FColor::Red, TEXT("今日信件数量已达上限，无法发送"));
	}
	else
	{
		SendLetter();
	}
}

void AMainController::OnGenerateLetter()
{
	if (SendLetterWidgetInstance)
	{
		// 获取输入内容
		const FString Content = SendLetterWidgetInstance -> GetLetterContent();
		if (Content . IsEmpty() || Content . TrimStart() . TrimEnd() . IsEmpty())
		{
			GEngine -> AddOnScreenDebugMessage(-1, 5, FColor::Red, TEXT("信件内容不能为空"));
			return;
		}
		if (LettersSubclassOf . IsEmpty()) { return; }
		const int Index = FMath::RandRange(0, LettersSubclassOf . Num() - 1);
		if (!LettersSubclassOf . IsValidIndex(Index) || !LettersSubclassOf[Index]) { return; }
		const ACharacter* OwnerCharacter = GetCharacter();
		if (OwnerCharacter)
		{
			const FVector LocalSpawnLocation = OwnerCharacter -> GetActorLocation();
			const FRotator LocalSpawnRotation = OwnerCharacter -> GetActorRotation();
			// 发送信件信息到服务器
			if (MainGameInstance)
			{
				if (ANetManager* Manager = MainGameInstance -> GetNetManager())
				{
					ULog::Info(TEXT("【OnGenerateLetter】发送信件数据"), false);
					NetPacket::FLetterData LetterPayload;
					LetterPayload . SetContent(Content);
					LetterPayload . SetStyleIndex(Index);
					LetterPayload . SetLocation(LocalSpawnLocation);
					LetterPayload . SetRotation(LocalSpawnRotation);
					Manager -> SendLetterData(LetterPayload);
				}
			}
		}

		SendLetterWidgetInstance -> Close();
		SendLetterWidgetInstance -> SetInputGame();

		MainCharacter -> GetCharacterMovement() -> SetMovementMode(MOVE_Walking);
	}
	bIsSend = false;
}

void AMainController::OnCancel()
{
	if (SendLetterWidgetInstance)
	{
		SendLetterWidgetInstance -> Close();
		SendLetterWidgetInstance -> SetInputGame();
	}
	if (ReadLetterWidgetInstance)
	{
		ReadLetterWidgetInstance -> Close();
		ReadLetterWidgetInstance -> SetInputGame();
	}

	bIsSend = false;
	bIsRead = false;
	MainCharacter -> GetCharacterMovement() -> SetMovementMode(MOVE_Walking);
}

void AMainController::OnLoginRequest()
{
	if (LoginSubclassOf)
	{
		LoginWidget = CreateWidget<ULoginWidget>(GetWorld(),LoginSubclassOf);
		if (LoginWidget)
		{
			LoginWidget->AddToViewport(100);
			LoginWidget->Open();

			if (UButton* LoginButton = LoginWidget->GetLoginButton())
			{
				LoginButton->OnClicked.AddDynamic(this, &AMainController::OnLoginJudge);
			}
			if (UButton* ExitButton = LoginWidget->GetExitButton())
			{
				ExitButton->OnClicked.AddDynamic(this, &AMainController::OnExit);
			}

		}
	}
}

void AMainController::OnLoginJudge(){
	Username = LoginWidget->GetAccountData();
	Password = LoginWidget->GetPasswordData();
	if (!Username)
	{
		GEngine -> AddOnScreenDebugMessage(-1, 5, FColor::Red, TEXT("账号不能为空"));
		return;
	}
	if (Password . IsEmpty() || Password . TrimStart() . TrimEnd() . IsEmpty())
	{
		GEngine -> AddOnScreenDebugMessage(-1, 5, FColor::Red, TEXT("密码不能为空"));
		return;
	}

	if (MainGameInstance)
	{
		if (ANetManager* Manager = MainGameInstance -> GetNetManager())
		{
			// 使用新的用户登录方法
			FString UsernameStr = FString::FromInt(Username);
			Manager->SendUserLogin(UsernameStr, Password);
		}
	}
}

void AMainController::OnExit()
{
	LoginWidget->Close();
}

void AMainController::OnLoginVerify(bool bSuccess, const FString& Message)
{
	if (bSuccess)
	{
		ULog::Info(TEXT("【Net】登录成功"), true);
		LoginWidget->Close();
		MainMenuWidget->SetInputGame();
		MainMenuWidget->Close();
		MainCharacter->LoginSuccess();
	}
	else
	{
		GEngine->AddOnScreenDebugMessage(-1, 5, FColor::Red, Message);
	}
}

void AMainController::ReadLetter()
{
	// const FVector Start = MainCharacter -> GetActorLocation();
	const FVector Start = MainCharacter -> GetActorLocation() - FVector(0, 0, MainCharacter -> GetCapsuleComponent() -> GetScaledCapsuleHalfHeight());
	const FVector ForwardVector = MainCharacter -> GetActorForwardVector();
	const FVector End = ((ForwardVector * 200.f) + Start);

	FHitResult HitResult;
	FCollisionQueryParams CollisionParams;
	CollisionParams . AddIgnoredActor(GetOwner());

	// 绘制调试射线
	// DrawDebugLine(GetWorld(), Start, End, FColor::Red, false, 2.f);

	if (GetWorld() -> LineTraceSingleByChannel(HitResult, Start, End, ECC_Visibility, CollisionParams))
	{
		ALetterObject* HitLetterObject = Cast<ALetterObject>(HitResult . GetActor());

		UE_LOG(LogTemp, Warning, TEXT("Hit Actor: %s"), *HitResult.GetActor()->GetName());

		if (HitLetterObject)
		{
			// 初始化UI
			ReadLetterWidgetInstance = CreateWidget<UReadLetterWidget>(GetWorld(), ReadLetterWidget);
			if (ReadLetterWidgetInstance)
			{
				bIsRead = true;
				MainCharacter -> GetCharacterMovement() -> StopMovementImmediately();
				MainCharacter -> GetCharacterMovement() -> SetMovementMode(MOVE_None);

				ReadLetterWidgetInstance -> Open();
				ReadLetterWidgetInstance -> AddToViewport();

				ReadLetterWidgetInstance -> SetInputUI();

				const FString Message = HitLetterObject -> GetLetterContent();
				ReadLetterWidgetInstance -> SetLetterContent(Message);

				// 绑定UI按钮事件
				if (UButton* CloseButton = ReadLetterWidgetInstance -> GetCloseButton())
				{
					CloseButton -> OnClicked . AddDynamic(this, &AMainController::OnCancel);
				}
			}
		}
	}
}

void AMainController::WaveHand()
{
	if (MainCharacter)
	{
		if (UMainAnimInstance* AnimInstance = MainCharacter->GetAnimInstance())
		{
			if (!AnimInstance->IsWaving())
			{
				AnimInstance->SetIsWaving(true);
				AnimInstance->PlayWaveHandAnimation();
				// 绑定动画结束事件
				MainCharacter->GetAnimInstance()->OnMontageEnded.AddDynamic(
					this, &AMainController::OnWaveAnimationEnded
				);
			}
		}
	}
}

void AMainController::OnWaveAnimationEnded(UAnimMontage* Montage, bool bInterrupted)
{
	if (MainCharacter && Montage)
	{
		UMainAnimInstance* AnimInstance = MainCharacter->GetAnimInstance();
		if (AnimInstance)
		{
			AnimInstance->SetIsWaving(false);
		}
		MainCharacter->GetAnimInstance()->OnMontageEnded.RemoveDynamic(
			this, &AMainController::OnWaveAnimationEnded
		);
	}
}

void AMainController::ToggleSprint()
{
	bIsSprinting = !bIsSprinting;
	MainCharacter->GetCharacterMovement()->MaxWalkSpeed = bIsSprinting ? 1200 : 600; // 加速时速度为1200，恢复时为600
	ULog::Info(bIsSprinting ? TEXT("冲刺状态") : TEXT("跑步状态"), false);
}

void AMainController::ExitGame()
{
	if (!ExitWidgetInstance)
	{
		ExitWidgetInstance = CreateWidget<UExitWidget>(GetWorld(), ExitWidget);
	}
	if (ExitWidgetInstance)
	{
		if (ExitWidgetInstance->IsInViewport())
		{
			return;
		}
		MainCharacter->GetCharacterMovement()->StopMovementImmediately();
		MainCharacter->GetCharacterMovement()->SetMovementMode(MOVE_None);

		ExitWidgetInstance->Open();
		ExitWidgetInstance->AddToViewport(500);

		ExitWidgetInstance->SetInputUI();

		if (UButton* CancelButton = ExitWidgetInstance->GetCancelButton())
		{
			CancelButton->OnClicked.AddDynamic(this, &AMainController::HandleExitCancel);
		}
	}
}

void AMainController::HandleExitCancel()
{
	if (ExitWidgetInstance)
	{
		ExitWidgetInstance->Close();
		ExitWidgetInstance->SetInputGame();
		MainCharacter->GetCharacterMovement()->SetMovementMode(MOVE_Walking);
	}
	if (MainMenuWidget->IsInViewport())
	{
		MainMenuWidget->SetInputUI();
	}
}

void AMainController::OnRegisterRequest()
{
	if (RegisterSubclassOf)
	{
		RegisterWidget = CreateWidget<URegisterWidget>(GetWorld(), RegisterSubclassOf);
		if (RegisterWidget)
		{
			RegisterWidget->AddToViewport(100);
			RegisterWidget->Open();

			if (UButton* RegisterButton = RegisterWidget->GetRegisterButton())
			{
				RegisterButton->OnClicked.AddDynamic(this, &AMainController::OnRegisterJudge);
			}
			if (UButton* BackButton = RegisterWidget->GetBackButton())
			{
				BackButton->OnClicked.AddDynamic(this, &AMainController::OnBackToLogin);
			}
		}
	}
}

void AMainController::OnRegisterJudge()
{
	if (!RegisterWidget)
	{
		return;
	}

	FString RegisterUsername = RegisterWidget->GetUsernameData();
	FString RegisterPassword = RegisterWidget->GetPasswordData();
	FString RegisterEmail = RegisterWidget->GetEmailData();

	// 基本验证已在RegisterWidget中完成，这里直接发送注册请求
	if (MainGameInstance)
	{
		if (ANetManager* Manager = MainGameInstance->GetNetManager())
		{
			Manager->SendRegister(RegisterUsername, RegisterPassword, RegisterEmail);
		}
	}
}

void AMainController::OnRegisterVerify(bool bSuccess, const FString& Message)
{
	if (bSuccess)
	{
		ULog::Info(TEXT("【Net】注册成功"), true);
		GEngine->AddOnScreenDebugMessage(-1, 5, FColor::Green, Message);

		// 注册成功后自动返回登录界面
		OnBackToLogin();
	}
	else
	{
		GEngine->AddOnScreenDebugMessage(-1, 5, FColor::Red, Message);
		if (RegisterWidget)
		{
			RegisterWidget->SetErrorMessage(Message);
		}
	}
}

void AMainController::OnBackToLogin()
{
	if (RegisterWidget)
	{
		RegisterWidget->Close();
		RegisterWidget = nullptr;
	}

	// 返回到主菜单
	if (MainMenuWidget && !MainMenuWidget->IsInViewport())
	{
		MainMenuWidget->AddToViewport(100);
		MainMenuWidget->Open();
	}
}
